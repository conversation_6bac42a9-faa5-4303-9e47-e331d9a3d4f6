#!/usr/bin/env python3
"""
Big River Coffee - Shopify Desktop Manager
A simple desktop application to manage your Shopify store
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import requests
import json
from datetime import datetime
import threading

class ShopifyDesktopApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Big River Coffee - Shopify Manager")
        self.root.geometry("1200x800")
        
        # Shopify API Configuration
        self.config = {
            'shop_domain': '9bc094',
            'access_token': '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',
            'api_version': '2025-07'
        }
        
        self.base_url = f"https://{self.config['shop_domain']}.myshopify.com/admin/api/{self.config['api_version']}"
        self.headers = {
            'X-Shopify-Access-Token': self.config['access_token'],
            'Content-Type': 'application/json'
        }
        
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Dashboard Tab
        self.dashboard_frame = ttk.Frame(notebook)
        notebook.add(self.dashboard_frame, text='Dashboard')
        self.setup_dashboard_tab()
        
        # Products Tab
        self.products_frame = ttk.Frame(notebook)
        notebook.add(self.products_frame, text='Products')
        self.setup_products_tab()
        
        # Orders Tab
        self.orders_frame = ttk.Frame(notebook)
        notebook.add(self.orders_frame, text='Orders')
        self.setup_orders_tab()
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def setup_dashboard_tab(self):
        # Store info frame
        info_frame = ttk.LabelFrame(self.dashboard_frame, text="Store Information", padding=10)
        info_frame.pack(fill='x', padx=10, pady=5)
        
        self.store_info_text = scrolledtext.ScrolledText(info_frame, height=6, width=80)
        self.store_info_text.pack(fill='both', expand=True)
        
        # Quick stats frame
        stats_frame = ttk.LabelFrame(self.dashboard_frame, text="Quick Stats", padding=10)
        stats_frame.pack(fill='x', padx=10, pady=5)
        
        # Stats labels
        self.stats_labels = {}
        stats = ['Total Products', 'Active Products', 'Draft Products', 'Recent Orders']
        
        for i, stat in enumerate(stats):
            label = ttk.Label(stats_frame, text=f"{stat}:")
            label.grid(row=i//2, column=(i%2)*2, sticky='w', padx=5, pady=2)
            
            value_label = ttk.Label(stats_frame, text="Loading...", font=('Arial', 10, 'bold'))
            value_label.grid(row=i//2, column=(i%2)*2+1, sticky='w', padx=5, pady=2)
            self.stats_labels[stat] = value_label
        
        # Refresh button
        refresh_btn = ttk.Button(self.dashboard_frame, text="Refresh Data", command=self.load_data)
        refresh_btn.pack(pady=10)
    
    def setup_products_tab(self):
        # Products controls
        controls_frame = ttk.Frame(self.products_frame)
        controls_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(controls_frame, text="Products:").pack(side='left')
        
        refresh_products_btn = ttk.Button(controls_frame, text="Refresh Products", 
                                        command=self.load_products)
        refresh_products_btn.pack(side='right', padx=5)
        
        # Products treeview
        columns = ('ID', 'Title', 'Status', 'Type', 'Vendor', 'Created')
        self.products_tree = ttk.Treeview(self.products_frame, columns=columns, show='headings', height=20)
        
        # Configure columns
        for col in columns:
            self.products_tree.heading(col, text=col)
            if col == 'ID':
                self.products_tree.column(col, width=100)
            elif col == 'Title':
                self.products_tree.column(col, width=300)
            else:
                self.products_tree.column(col, width=100)
        
        # Scrollbar for products
        products_scrollbar = ttk.Scrollbar(self.products_frame, orient='vertical', command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=products_scrollbar.set)
        
        self.products_tree.pack(side='left', fill='both', expand=True, padx=10, pady=5)
        products_scrollbar.pack(side='right', fill='y', pady=5)
        
        # Product actions
        actions_frame = ttk.Frame(self.products_frame)
        actions_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(actions_frame, text="Activate Selected", 
                  command=lambda: self.update_product_status('active')).pack(side='left', padx=5)
        ttk.Button(actions_frame, text="Draft Selected", 
                  command=lambda: self.update_product_status('draft')).pack(side='left', padx=5)
    
    def setup_orders_tab(self):
        # Orders controls
        controls_frame = ttk.Frame(self.orders_frame)
        controls_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(controls_frame, text="Recent Orders:").pack(side='left')
        
        refresh_orders_btn = ttk.Button(controls_frame, text="Refresh Orders", 
                                      command=self.load_orders)
        refresh_orders_btn.pack(side='right', padx=5)
        
        # Orders treeview
        order_columns = ('Order #', 'Customer', 'Total', 'Status', 'Date')
        self.orders_tree = ttk.Treeview(self.orders_frame, columns=order_columns, show='headings', height=20)
        
        # Configure order columns
        for col in order_columns:
            self.orders_tree.heading(col, text=col)
            if col == 'Customer':
                self.orders_tree.column(col, width=200)
            elif col == 'Order #':
                self.orders_tree.column(col, width=100)
            else:
                self.orders_tree.column(col, width=120)
        
        # Scrollbar for orders
        orders_scrollbar = ttk.Scrollbar(self.orders_frame, orient='vertical', command=self.orders_tree.yview)
        self.orders_tree.configure(yscrollcommand=orders_scrollbar.set)
        
        self.orders_tree.pack(side='left', fill='both', expand=True, padx=10, pady=5)
        orders_scrollbar.pack(side='right', fill='y', pady=5)
    
    def make_request(self, endpoint, method='GET', data=None):
        """Make API request to Shopify"""
        try:
            url = f"{self.base_url}/{endpoint}"
            
            if method == 'GET':
                response = requests.get(url, headers=self.headers)
            elif method == 'PUT':
                response = requests.put(url, headers=self.headers, json=data)
            elif method == 'POST':
                response = requests.post(url, headers=self.headers, json=data)
            
            response.raise_for_status()
            return response.json()
        
        except requests.exceptions.RequestException as e:
            messagebox.showerror("API Error", f"Failed to connect to Shopify: {str(e)}")
            return None
    
    def load_data(self):
        """Load all data in background thread"""
        self.status_var.set("Loading data...")
        threading.Thread(target=self._load_data_thread, daemon=True).start()
    
    def _load_data_thread(self):
        """Background thread for loading data"""
        try:
            # Load shop info
            shop_data = self.make_request('shop.json')
            if shop_data:
                shop = shop_data['shop']
                info_text = f"""Store: {shop['name']}
Domain: {shop['myshopify_domain']}
Email: {shop['email']}
Currency: {shop['currency']}
Plan: {shop.get('plan_name', 'N/A')}
Created: {shop['created_at'][:10]}"""
                
                self.root.after(0, lambda: self.store_info_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.store_info_text.insert(1.0, info_text))
            
            # Load products for stats
            products_data = self.make_request('products.json?limit=250')
            if products_data:
                products = products_data['products']
                total_products = len(products)
                active_products = len([p for p in products if p['status'] == 'active'])
                draft_products = len([p for p in products if p['status'] == 'draft'])
                
                self.root.after(0, lambda: self.stats_labels['Total Products'].config(text=str(total_products)))
                self.root.after(0, lambda: self.stats_labels['Active Products'].config(text=str(active_products)))
                self.root.after(0, lambda: self.stats_labels['Draft Products'].config(text=str(draft_products)))
            
            # Load recent orders for stats
            orders_data = self.make_request('orders.json?limit=50')
            if orders_data:
                orders = orders_data['orders']
                recent_orders = len(orders)
                self.root.after(0, lambda: self.stats_labels['Recent Orders'].config(text=str(recent_orders)))
            
            self.root.after(0, lambda: self.status_var.set("Data loaded successfully"))
            
        except Exception as e:
            self.root.after(0, lambda: self.status_var.set(f"Error loading data: {str(e)}"))
            self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to load data: {str(e)}"))

    def load_products(self):
        """Load products in background thread"""
        self.status_var.set("Loading products...")
        threading.Thread(target=self._load_products_thread, daemon=True).start()

    def _load_products_thread(self):
        """Background thread for loading products"""
        try:
            products_data = self.make_request('products.json?limit=250')
            if products_data:
                products = products_data['products']

                # Clear existing items
                self.root.after(0, lambda: self.products_tree.delete(*self.products_tree.get_children()))

                # Add products to tree
                for product in products:
                    product_id = str(product['id'])
                    title = product['title']
                    status = product['status'].title()
                    product_type = product.get('product_type', 'N/A')
                    vendor = product.get('vendor', 'N/A')
                    created = product['created_at'][:10]

                    self.root.after(0, lambda p=product: self.products_tree.insert('', 'end',
                                    values=(p['id'], p['title'], p['status'].title(),
                                           p.get('product_type', 'N/A'), p.get('vendor', 'N/A'),
                                           p['created_at'][:10])))

                self.root.after(0, lambda: self.status_var.set(f"Loaded {len(products)} products"))

        except Exception as e:
            self.root.after(0, lambda: self.status_var.set(f"Error loading products: {str(e)}"))
            self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to load products: {str(e)}"))

    def load_orders(self):
        """Load orders in background thread"""
        self.status_var.set("Loading orders...")
        threading.Thread(target=self._load_orders_thread, daemon=True).start()

    def _load_orders_thread(self):
        """Background thread for loading orders"""
        try:
            orders_data = self.make_request('orders.json?limit=100')
            if orders_data:
                orders = orders_data['orders']

                # Clear existing items
                self.root.after(0, lambda: self.orders_tree.delete(*self.orders_tree.get_children()))

                # Add orders to tree
                for order in orders:
                    order_number = order.get('order_number', order['id'])
                    customer = 'Guest'
                    if order.get('customer'):
                        customer = f"{order['customer'].get('first_name', '')} {order['customer'].get('last_name', '')}".strip()
                        if not customer:
                            customer = order['customer'].get('email', 'Guest')

                    total = f"${float(order['total_price']):.2f}"
                    status = order['financial_status'].title() if order.get('financial_status') else 'N/A'
                    date = order['created_at'][:10]

                    self.root.after(0, lambda o=order: self.orders_tree.insert('', 'end',
                                    values=(o.get('order_number', o['id']),
                                           self._get_customer_name(o),
                                           f"${float(o['total_price']):.2f}",
                                           o.get('financial_status', 'N/A').title(),
                                           o['created_at'][:10])))

                self.root.after(0, lambda: self.status_var.set(f"Loaded {len(orders)} orders"))

        except Exception as e:
            self.root.after(0, lambda: self.status_var.set(f"Error loading orders: {str(e)}"))
            self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to load orders: {str(e)}"))

    def _get_customer_name(self, order):
        """Helper to get customer name from order"""
        if order.get('customer'):
            customer = f"{order['customer'].get('first_name', '')} {order['customer'].get('last_name', '')}".strip()
            if not customer:
                customer = order['customer'].get('email', 'Guest')
            return customer
        return 'Guest'

    def update_product_status(self, new_status):
        """Update selected product status"""
        selected = self.products_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a product to update.")
            return

        # Get product ID from selected item
        item = self.products_tree.item(selected[0])
        product_id = item['values'][0]

        self.status_var.set(f"Updating product {product_id} to {new_status}...")
        threading.Thread(target=self._update_product_status_thread,
                        args=(product_id, new_status), daemon=True).start()

    def _update_product_status_thread(self, product_id, new_status):
        """Background thread for updating product status"""
        try:
            data = {
                'product': {
                    'id': product_id,
                    'status': new_status
                }
            }

            result = self.make_request(f'products/{product_id}.json', method='PUT', data=data)
            if result:
                self.root.after(0, lambda: self.status_var.set(f"Product {product_id} updated to {new_status}"))
                self.root.after(0, self.load_products)  # Refresh products list

        except Exception as e:
            self.root.after(0, lambda: self.status_var.set(f"Error updating product: {str(e)}"))
            self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to update product: {str(e)}"))


def main():
    root = tk.Tk()
    app = ShopifyDesktopApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
