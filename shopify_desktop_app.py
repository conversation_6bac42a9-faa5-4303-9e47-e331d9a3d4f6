#!/usr/bin/env python3
"""
Big River Coffee - Shopify Desktop Manager
A simple desktop application to manage your Shopify store
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import requests
import json
from datetime import datetime
import threading
import base64
import os

class ShopifyDesktopApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Big River Coffee - Shopify Manager")
        self.root.geometry("1200x800")
        
        # Shopify API Configuration
        self.config = {
            'shop_domain': '9bc094',
            'access_token': '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',
            'api_version': '2025-07'
        }

        # Gemini AI Configuration
        self.gemini_api_key = 'AIzaSyD9DKZ0zV_OWko4hqo15azaEL8A6jHC3-c'
        self.gemini_url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent'
        
        self.base_url = f"https://{self.config['shop_domain']}.myshopify.com/admin/api/{self.config['api_version']}"
        self.headers = {
            'X-Shopify-Access-Token': self.config['access_token'],
            'Content-Type': 'application/json'
        }
        
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Dashboard Tab
        self.dashboard_frame = ttk.Frame(notebook)
        notebook.add(self.dashboard_frame, text='Dashboard')
        self.setup_dashboard_tab()
        
        # Products Tab
        self.products_frame = ttk.Frame(notebook)
        notebook.add(self.products_frame, text='Products')
        self.setup_products_tab()
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def setup_dashboard_tab(self):
        # Store info frame
        info_frame = ttk.LabelFrame(self.dashboard_frame, text="Store Information", padding=10)
        info_frame.pack(fill='x', padx=10, pady=5)
        
        self.store_info_text = scrolledtext.ScrolledText(info_frame, height=6, width=80)
        self.store_info_text.pack(fill='both', expand=True)
        
        # Quick stats frame
        stats_frame = ttk.LabelFrame(self.dashboard_frame, text="Product Statistics", padding=10)
        stats_frame.pack(fill='x', padx=10, pady=5)

        # Stats labels
        self.stats_labels = {}
        stats = ['Total Products', 'Active Products', 'Draft Products']

        for i, stat in enumerate(stats):
            label = ttk.Label(stats_frame, text=f"{stat}:")
            label.grid(row=i//3, column=(i%3)*2, sticky='w', padx=5, pady=2)

            value_label = ttk.Label(stats_frame, text="Loading...", font=('Arial', 10, 'bold'))
            value_label.grid(row=i//3, column=(i%3)*2+1, sticky='w', padx=5, pady=2)
            self.stats_labels[stat] = value_label
        
        # Refresh button
        refresh_btn = ttk.Button(self.dashboard_frame, text="Refresh Data", command=self.load_data)
        refresh_btn.pack(pady=10)
    
    def setup_products_tab(self):
        # Products controls
        controls_frame = ttk.Frame(self.products_frame)
        controls_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(controls_frame, text="Products:").pack(side='left')
        
        refresh_products_btn = ttk.Button(controls_frame, text="Refresh Products", 
                                        command=self.load_products)
        refresh_products_btn.pack(side='right', padx=5)
        
        # Products treeview
        columns = ('ID', 'Title', 'Status', 'Type', 'Vendor', 'Created')
        self.products_tree = ttk.Treeview(self.products_frame, columns=columns, show='headings', height=20)
        
        # Configure columns
        for col in columns:
            self.products_tree.heading(col, text=col)
            if col == 'ID':
                self.products_tree.column(col, width=100)
            elif col == 'Title':
                self.products_tree.column(col, width=300)
            else:
                self.products_tree.column(col, width=100)
        
        # Scrollbar for products
        products_scrollbar = ttk.Scrollbar(self.products_frame, orient='vertical', command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=products_scrollbar.set)
        
        self.products_tree.pack(side='left', fill='both', expand=True, padx=10, pady=5)
        products_scrollbar.pack(side='right', fill='y', pady=5)
        
        # Product actions
        actions_frame = ttk.Frame(self.products_frame)
        actions_frame.pack(fill='x', padx=10, pady=5)

        ttk.Button(actions_frame, text="Create New Product",
                  command=self.open_create_product_window).pack(side='left', padx=5)
        ttk.Button(actions_frame, text="Create from Coffee Template",
                  command=self.open_coffee_template_window).pack(side='left', padx=5)
        ttk.Button(actions_frame, text="Activate Selected",
                  command=lambda: self.update_product_status('active')).pack(side='left', padx=5)
        ttk.Button(actions_frame, text="Draft Selected",
                  command=lambda: self.update_product_status('draft')).pack(side='left', padx=5)
        ttk.Button(actions_frame, text="View Selected",
                  command=self.view_product_details).pack(side='left', padx=5)
    

    def make_request(self, endpoint, method='GET', data=None):
        """Make API request to Shopify"""
        try:
            url = f"{self.base_url}/{endpoint}"
            
            if method == 'GET':
                response = requests.get(url, headers=self.headers)
            elif method == 'PUT':
                response = requests.put(url, headers=self.headers, json=data)
            elif method == 'POST':
                response = requests.post(url, headers=self.headers, json=data)
            
            response.raise_for_status()
            return response.json()
        
        except requests.exceptions.RequestException as e:
            messagebox.showerror("API Error", f"Failed to connect to Shopify: {str(e)}")
            return None
    
    def load_data(self):
        """Load all data in background thread"""
        self.status_var.set("Loading data...")
        threading.Thread(target=self._load_data_thread, daemon=True).start()
    
    def _load_data_thread(self):
        """Background thread for loading data"""
        try:
            # Load shop info
            shop_data = self.make_request('shop.json')
            if shop_data:
                shop = shop_data['shop']
                info_text = f"""Store: {shop['name']}
Domain: {shop['myshopify_domain']}
Email: {shop['email']}
Currency: {shop['currency']}
Plan: {shop.get('plan_name', 'N/A')}
Created: {shop['created_at'][:10]}"""
                
                self.root.after(0, lambda: self.store_info_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.store_info_text.insert(1.0, info_text))
            
            # Load products for stats
            products_data = self.make_request('products.json?limit=250')
            if products_data:
                products = products_data['products']
                total_products = len(products)
                active_products = len([p for p in products if p['status'] == 'active'])
                draft_products = len([p for p in products if p['status'] == 'draft'])
                
                self.root.after(0, lambda: self.stats_labels['Total Products'].config(text=str(total_products)))
                self.root.after(0, lambda: self.stats_labels['Active Products'].config(text=str(active_products)))
                self.root.after(0, lambda: self.stats_labels['Draft Products'].config(text=str(draft_products)))
            

            
            self.root.after(0, lambda: self.status_var.set("Data loaded successfully"))
            
        except Exception as e:
            self.root.after(0, lambda: self.status_var.set(f"Error loading data: {str(e)}"))
            self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to load data: {str(e)}"))

    def load_products(self):
        """Load products in background thread"""
        self.status_var.set("Loading products...")
        threading.Thread(target=self._load_products_thread, daemon=True).start()

    def _load_products_thread(self):
        """Background thread for loading products"""
        try:
            products_data = self.make_request('products.json?limit=250')
            if products_data:
                products = products_data['products']

                # Clear existing items
                self.root.after(0, lambda: self.products_tree.delete(*self.products_tree.get_children()))

                # Add products to tree
                for product in products:
                    product_id = str(product['id'])
                    title = product['title']
                    status = product['status'].title()
                    product_type = product.get('product_type', 'N/A')
                    vendor = product.get('vendor', 'N/A')
                    created = product['created_at'][:10]

                    self.root.after(0, lambda p=product: self.products_tree.insert('', 'end',
                                    values=(p['id'], p['title'], p['status'].title(),
                                           p.get('product_type', 'N/A'), p.get('vendor', 'N/A'),
                                           p['created_at'][:10])))

                self.root.after(0, lambda: self.status_var.set(f"Loaded {len(products)} products"))

        except Exception as e:
            self.root.after(0, lambda: self.status_var.set(f"Error loading products: {str(e)}"))
            self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to load products: {str(e)}"))


    def update_product_status(self, new_status):
        """Update selected product status"""
        selected = self.products_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a product to update.")
            return

        # Get product ID from selected item
        item = self.products_tree.item(selected[0])
        product_id = item['values'][0]

        self.status_var.set(f"Updating product {product_id} to {new_status}...")
        threading.Thread(target=self._update_product_status_thread,
                        args=(product_id, new_status), daemon=True).start()

    def _update_product_status_thread(self, product_id, new_status):
        """Background thread for updating product status"""
        try:
            data = {
                'product': {
                    'id': product_id,
                    'status': new_status
                }
            }

            result = self.make_request(f'products/{product_id}.json', method='PUT', data=data)
            if result:
                self.root.after(0, lambda: self.status_var.set(f"Product {product_id} updated to {new_status}"))
                self.root.after(0, self.load_products)  # Refresh products list

        except Exception as e:
            self.root.after(0, lambda: self.status_var.set(f"Error updating product: {str(e)}"))
            self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to update product: {str(e)}"))

    def view_product_details(self):
        """View detailed information about selected product"""
        selected = self.products_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a product to view.")
            return

        # Get product ID from selected item
        item = self.products_tree.item(selected[0])
        product_id = item['values'][0]

        self.status_var.set(f"Loading product details for {product_id}...")
        threading.Thread(target=self._view_product_details_thread,
                        args=(product_id,), daemon=True).start()

    def _view_product_details_thread(self, product_id):
        """Background thread for loading product details"""
        try:
            product_data = self.make_request(f'products/{product_id}.json')
            if product_data:
                product = product_data['product']
                self.root.after(0, lambda: self._show_product_details_window(product))
                self.root.after(0, lambda: self.status_var.set("Product details loaded"))

        except Exception as e:
            self.root.after(0, lambda: self.status_var.set(f"Error loading product: {str(e)}"))
            self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to load product: {str(e)}"))

    def _show_product_details_window(self, product):
        """Show product details in a new window"""
        details_window = tk.Toplevel(self.root)
        details_window.title(f"Product Details - {product['title']}")
        details_window.geometry("600x500")

        # Create scrolled text for product details
        details_text = scrolledtext.ScrolledText(details_window, wrap=tk.WORD)
        details_text.pack(fill='both', expand=True, padx=10, pady=10)

        # Format product information
        details = f"""PRODUCT INFORMATION
{'='*50}

Title: {product['title']}
Handle: {product['handle']}
Status: {product['status'].title()}
Product Type: {product.get('product_type', 'N/A')}
Vendor: {product.get('vendor', 'N/A')}
Created: {product['created_at']}
Updated: {product['updated_at']}

DESCRIPTION
{'='*50}
{product.get('body_html', 'No description available')}

VARIANTS
{'='*50}"""

        for i, variant in enumerate(product.get('variants', []), 1):
            details += f"""
Variant {i}:
  Title: {variant.get('title', 'Default Title')}
  Price: ${variant.get('price', '0.00')}
  SKU: {variant.get('sku', 'N/A')}
  Inventory: {variant.get('inventory_quantity', 0)}
  Weight: {variant.get('weight', 0)} {variant.get('weight_unit', 'kg')}
"""

        if product.get('tags'):
            details += f"\nTAGS\n{'='*50}\n{product['tags']}\n"

        if product.get('images'):
            details += f"\nIMAGES\n{'='*50}\n"
            for i, image in enumerate(product['images'], 1):
                details += f"Image {i}: {image.get('src', 'N/A')}\n"

        details_text.insert(1.0, details)
        details_text.config(state='disabled')  # Make read-only

    def open_create_product_window(self):
        """Open window to create a new product"""
        create_window = tk.Toplevel(self.root)
        create_window.title("Create New Product")
        create_window.geometry("700x600")

        # Create form fields
        form_frame = ttk.Frame(create_window)
        form_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Product title
        ttk.Label(form_frame, text="Product Title:").grid(row=0, column=0, sticky='w', pady=5)
        self.title_entry = ttk.Entry(form_frame, width=50)
        self.title_entry.grid(row=0, column=1, columnspan=2, sticky='ew', pady=5)

        # Product type
        ttk.Label(form_frame, text="Product Type:").grid(row=1, column=0, sticky='w', pady=5)
        self.type_entry = ttk.Entry(form_frame, width=30)
        self.type_entry.grid(row=1, column=1, sticky='ew', pady=5)
        self.type_entry.insert(0, "Coffee")  # Default for coffee business

        # Vendor
        ttk.Label(form_frame, text="Vendor:").grid(row=2, column=0, sticky='w', pady=5)
        self.vendor_entry = ttk.Entry(form_frame, width=30)
        self.vendor_entry.grid(row=2, column=1, sticky='ew', pady=5)
        self.vendor_entry.insert(0, "Big River Coffee")  # Default vendor

        # Status
        ttk.Label(form_frame, text="Status:").grid(row=3, column=0, sticky='w', pady=5)
        self.status_var_create = tk.StringVar(value="draft")
        status_combo = ttk.Combobox(form_frame, textvariable=self.status_var_create,
                                   values=["draft", "active"], state="readonly")
        status_combo.grid(row=3, column=1, sticky='ew', pady=5)

        # Description
        ttk.Label(form_frame, text="Description:").grid(row=4, column=0, sticky='nw', pady=5)
        self.description_text = scrolledtext.ScrolledText(form_frame, height=8, width=50)
        self.description_text.grid(row=4, column=1, columnspan=2, sticky='ew', pady=5)

        # Tags
        ttk.Label(form_frame, text="Tags (comma separated):").grid(row=5, column=0, sticky='w', pady=5)
        self.tags_entry = ttk.Entry(form_frame, width=50)
        self.tags_entry.grid(row=5, column=1, columnspan=2, sticky='ew', pady=5)
        self.tags_entry.insert(0, "coffee, premium, arabica")  # Default tags

        # Variant section
        variant_frame = ttk.LabelFrame(form_frame, text="Product Variant", padding=10)
        variant_frame.grid(row=6, column=0, columnspan=3, sticky='ew', pady=10)

        # Price
        ttk.Label(variant_frame, text="Price ($):").grid(row=0, column=0, sticky='w', pady=2)
        self.price_entry = ttk.Entry(variant_frame, width=15)
        self.price_entry.grid(row=0, column=1, sticky='w', pady=2)
        self.price_entry.insert(0, "19.99")  # Default price

        # SKU
        ttk.Label(variant_frame, text="SKU:").grid(row=0, column=2, sticky='w', padx=(20,0), pady=2)
        self.sku_entry = ttk.Entry(variant_frame, width=20)
        self.sku_entry.grid(row=0, column=3, sticky='w', pady=2)

        # Inventory
        ttk.Label(variant_frame, text="Inventory Quantity:").grid(row=1, column=0, sticky='w', pady=2)
        self.inventory_entry = ttk.Entry(variant_frame, width=15)
        self.inventory_entry.grid(row=1, column=1, sticky='w', pady=2)
        self.inventory_entry.insert(0, "100")  # Default inventory

        # Weight
        ttk.Label(variant_frame, text="Weight (kg):").grid(row=1, column=2, sticky='w', padx=(20,0), pady=2)
        self.weight_entry = ttk.Entry(variant_frame, width=15)
        self.weight_entry.grid(row=1, column=3, sticky='w', pady=2)
        self.weight_entry.insert(0, "0.34")  # Default weight for 12oz coffee

        # Buttons
        button_frame = ttk.Frame(form_frame)
        button_frame.grid(row=7, column=0, columnspan=3, pady=20)

        ttk.Button(button_frame, text="Create Product",
                  command=lambda: self.create_product(create_window)).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Cancel",
                  command=create_window.destroy).pack(side='left', padx=5)

        # Configure grid weights
        form_frame.columnconfigure(1, weight=1)
        variant_frame.columnconfigure(1, weight=1)
        variant_frame.columnconfigure(3, weight=1)

    def create_product(self, create_window):
        """Create a new product with the form data"""
        try:
            # Validate required fields
            if not self.title_entry.get().strip():
                messagebox.showerror("Validation Error", "Product title is required.")
                return

            if not self.price_entry.get().strip():
                messagebox.showerror("Validation Error", "Price is required.")
                return

            # Prepare product data
            product_data = {
                "product": {
                    "title": self.title_entry.get().strip(),
                    "body_html": self.description_text.get(1.0, tk.END).strip(),
                    "vendor": self.vendor_entry.get().strip() or "Big River Coffee",
                    "product_type": self.type_entry.get().strip() or "Coffee",
                    "status": self.status_var_create.get(),
                    "tags": self.tags_entry.get().strip(),
                    "variants": [
                        {
                            "price": self.price_entry.get().strip(),
                            "sku": self.sku_entry.get().strip(),
                            "inventory_quantity": int(self.inventory_entry.get().strip() or 0),
                            "weight": float(self.weight_entry.get().strip() or 0),
                            "weight_unit": "kg",
                            "inventory_management": "shopify",
                            "inventory_policy": "deny"
                        }
                    ]
                }
            }

            # Show creating message
            self.status_var.set("Creating product...")
            create_window.destroy()  # Close the form window

            # Create product in background thread
            threading.Thread(target=self._create_product_thread,
                           args=(product_data,), daemon=True).start()

        except ValueError as e:
            messagebox.showerror("Validation Error", "Please check numeric fields (price, inventory, weight).")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to create product: {str(e)}")

    def _create_product_thread(self, product_data):
        """Background thread for creating product"""
        try:
            result = self.make_request('products.json', method='POST', data=product_data)
            if result:
                product = result['product']
                self.root.after(0, lambda: self.status_var.set(f"Product '{product['title']}' created successfully!"))
                self.root.after(0, lambda: messagebox.showinfo("Success",
                                f"Product '{product['title']}' has been created successfully!\nProduct ID: {product['id']}"))
                self.root.after(0, self.load_products)  # Refresh products list

        except Exception as e:
            self.root.after(0, lambda: self.status_var.set(f"Error creating product: {str(e)}"))
            self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to create product: {str(e)}"))

    def open_coffee_template_window(self):
        """Open window to create a new coffee product using the Big River Coffee template"""
        template_window = tk.Toplevel(self.root)
        template_window.title("Create New Coffee Product - Big River Template")
        template_window.geometry("800x700")
        template_window.transient(self.root)  # Keep window on top of main window
        template_window.grab_set()  # Make window modal

        # Store reference to template window for image upload
        self.current_template_window = template_window

        # Create form fields
        form_frame = ttk.Frame(template_window)
        form_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Template info
        info_frame = ttk.LabelFrame(form_frame, text="Coffee Template with AI Analysis", padding=10)
        info_frame.pack(fill='x', pady=(0, 10))

        info_text = """This template creates a coffee product with the same structure as Cowboy Coffee:
• 5 standard variants (12oz whole bean, 12oz ground, 2lb whole bean, 2lb ground, 5lb whole bean)
• Professional coffee description template with fillable placeholders
• AI-powered product analysis from uploaded images
• Big River Coffee branding and tags
• Standard pricing structure"""

        ttk.Label(info_frame, text=info_text, justify='left').pack(anchor='w')

        # Image upload section
        image_frame = ttk.LabelFrame(form_frame, text="Product Image Analysis (Optional)", padding=10)
        image_frame.pack(fill='x', pady=(0, 10))

        # Image upload controls
        upload_frame = ttk.Frame(image_frame)
        upload_frame.pack(fill='x')

        ttk.Button(upload_frame, text="Upload Product Image",
                  command=self.upload_product_image).pack(side='left', padx=(0, 10))

        self.image_status_label = ttk.Label(upload_frame, text="No image selected")
        self.image_status_label.pack(side='left')

        ttk.Button(upload_frame, text="Analyze with AI",
                  command=self.analyze_image_with_ai).pack(side='right')

        self.uploaded_image_path = None

        # Coffee name and details
        details_frame = ttk.LabelFrame(form_frame, text="Coffee Details", padding=10)
        details_frame.pack(fill='x', pady=(0, 10))

        # Coffee name
        ttk.Label(details_frame, text="Coffee Name:").grid(row=0, column=0, sticky='w', pady=5)
        self.coffee_name_entry = ttk.Entry(details_frame, width=40)
        self.coffee_name_entry.grid(row=0, column=1, columnspan=2, sticky='ew', pady=5)

        # Roast level
        ttk.Label(details_frame, text="Roast Level:").grid(row=1, column=0, sticky='w', pady=5)
        self.roast_level_var = tk.StringVar(value="Medium Roast")
        roast_combo = ttk.Combobox(details_frame, textvariable=self.roast_level_var,
                                  values=["Light Roast", "Medium Roast", "Dark Roast"], state="readonly")
        roast_combo.grid(row=1, column=1, sticky='ew', pady=5)

        # Origin
        ttk.Label(details_frame, text="Origin/Blend:").grid(row=2, column=0, sticky='w', pady=5)
        self.origin_entry = ttk.Entry(details_frame, width=30)
        self.origin_entry.grid(row=2, column=1, sticky='ew', pady=5)
        self.origin_entry.insert(0, "Colombian")

        # Flavor notes
        ttk.Label(details_frame, text="Flavor Notes:").grid(row=3, column=0, sticky='w', pady=5)
        self.flavor_entry = ttk.Entry(details_frame, width=50)
        self.flavor_entry.grid(row=3, column=1, columnspan=2, sticky='ew', pady=5)
        self.flavor_entry.insert(0, "Rich, smooth, with notes of chocolate and caramel")

        # Status
        ttk.Label(details_frame, text="Status:").grid(row=4, column=0, sticky='w', pady=5)
        self.template_status_var = tk.StringVar(value="draft")
        status_combo = ttk.Combobox(details_frame, textvariable=self.template_status_var,
                                   values=["draft", "active"], state="readonly")
        status_combo.grid(row=4, column=1, sticky='ew', pady=5)

        # Description preview
        desc_frame = ttk.LabelFrame(form_frame, text="Description Preview", padding=10)
        desc_frame.pack(fill='both', expand=True, pady=(0, 10))

        self.desc_preview = scrolledtext.ScrolledText(desc_frame, height=8, width=70)
        self.desc_preview.pack(fill='both', expand=True)

        # Update preview when coffee name changes
        self.coffee_name_entry.bind('<KeyRelease>', self.update_description_preview)
        self.roast_level_var.trace('w', self.update_description_preview)
        self.origin_entry.bind('<KeyRelease>', self.update_description_preview)
        self.flavor_entry.bind('<KeyRelease>', self.update_description_preview)

        # Variants info
        variants_frame = ttk.LabelFrame(form_frame, text="Standard Variants (Auto-Generated)", padding=10)
        variants_frame.pack(fill='x', pady=(0, 10))

        variants_text = """The following 5 variants will be created automatically:
• 12oz Whole Bean - $19.99
• 12oz Ground - $19.99
• 2lb Whole Bean - $31.99
• 2lb Ground - $31.99
• 5lb Whole Bean - $66.99"""

        ttk.Label(variants_frame, text=variants_text, justify='left').pack(anchor='w')

        # Buttons
        button_frame = ttk.Frame(form_frame)
        button_frame.pack(pady=20)

        ttk.Button(button_frame, text="Create Coffee Product",
                  command=lambda: self.create_coffee_from_template(template_window)).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Cancel",
                  command=self.close_template_window).pack(side='left', padx=5)

        # Handle window close event
        template_window.protocol("WM_DELETE_WINDOW", self.close_template_window)

        # Configure grid weights
        details_frame.columnconfigure(1, weight=1)

        # Initial preview update
        self.update_description_preview()

    def update_description_preview(self, *args):
        """Update the description preview based on current inputs"""
        try:
            coffee_name = self.coffee_name_entry.get().strip() or "[Coffee Name]"
            roast_level = self.roast_level_var.get()
            origin = self.origin_entry.get().strip() or "Colombian"
            flavor_notes = self.flavor_entry.get().strip() or "Rich, smooth, with notes of chocolate and caramel"

            description = self.generate_coffee_description(coffee_name, roast_level, origin, flavor_notes)

            self.desc_preview.delete(1.0, tk.END)
            self.desc_preview.insert(1.0, description)
        except:
            pass  # Ignore errors during preview updates

    def generate_coffee_description(self, name, roast_level, origin, flavor_notes):
        """Generate a professional coffee description using the Big River template with fillable fields"""
        return f"""<h2>{name} - {roast_level}</h2>

<p><strong>Experience the exceptional quality of Big River Coffee's {name}</strong></p>

<p>Our {name} is a carefully crafted {roast_level.lower()} featuring premium {origin} beans. This exceptional coffee delivers {flavor_notes.lower()}, making it perfect for any time of day.</p>

<h3>Product Details:</h3>
<ul>
<li><strong>Roast Level:</strong> [ROAST_LEVEL]</li>
<li><strong>Origin:</strong> [ORIGIN]</li>
<li><strong>Flavor Profile:</strong> [FLAVOR_PROFILE]</li>
<li><strong>Bean Type:</strong> [BEAN_TYPE]</li>
<li><strong>Processing:</strong> [PROCESSING_METHOD]</li>
<li><strong>Altitude:</strong> [ALTITUDE]</li>
<li><strong>Harvest Season:</strong> [HARVEST_SEASON]</li>
</ul>

<h3>Available Options:</h3>
<ul>
<li>12oz Whole Bean or Ground</li>
<li>2lb Whole Bean or Ground</li>
<li>5lb Whole Bean</li>
</ul>

<p><strong>Brewing Recommendations:</strong><br>
[BREWING_RECOMMENDATIONS]</p>

<p><strong>Tasting Notes:</strong><br>
[DETAILED_TASTING_NOTES]</p>

<p><em>Roasted fresh to order by Big River Coffee. Each bag is carefully sealed to preserve peak flavor and aroma.</em></p>"""

    def create_coffee_from_template(self, template_window):
        """Create a coffee product using the template"""
        try:
            # Validate required fields
            coffee_name = self.coffee_name_entry.get().strip()
            if not coffee_name:
                messagebox.showerror("Validation Error", "Coffee name is required.")
                return

            roast_level = self.roast_level_var.get()
            origin = self.origin_entry.get().strip() or "Colombian"
            flavor_notes = self.flavor_entry.get().strip() or "Rich, smooth, with notes of chocolate and caramel"

            # Generate product title
            product_title = f"{coffee_name} - {roast_level}"

            # Generate description
            description = self.generate_coffee_description(coffee_name, roast_level, origin, flavor_notes)

            # Create the 5 standard variants
            variants = [
                {
                    "title": "12oz / whole bean",
                    "price": "19.99",
                    "sku": f"BRC-{coffee_name.upper().replace(' ', '-')}-12OZ-WB",
                    "inventory_quantity": 100,
                    "weight": 0.34,
                    "weight_unit": "kg",
                    "inventory_management": "shopify",
                    "inventory_policy": "deny"
                },
                {
                    "title": "12oz / ground",
                    "price": "19.99",
                    "sku": f"BRC-{coffee_name.upper().replace(' ', '-')}-12OZ-GR",
                    "inventory_quantity": 100,
                    "weight": 0.34,
                    "weight_unit": "kg",
                    "inventory_management": "shopify",
                    "inventory_policy": "deny"
                },
                {
                    "title": "2lb / whole bean",
                    "price": "31.99",
                    "sku": f"BRC-{coffee_name.upper().replace(' ', '-')}-2LB-WB",
                    "inventory_quantity": 50,
                    "weight": 0.91,
                    "weight_unit": "kg",
                    "inventory_management": "shopify",
                    "inventory_policy": "deny"
                },
                {
                    "title": "2lb / ground",
                    "price": "31.99",
                    "sku": f"BRC-{coffee_name.upper().replace(' ', '-')}-2LB-GR",
                    "inventory_quantity": 50,
                    "weight": 0.91,
                    "weight_unit": "kg",
                    "inventory_management": "shopify",
                    "inventory_policy": "deny"
                },
                {
                    "title": "5lb / whole bean",
                    "price": "66.99",
                    "sku": f"BRC-{coffee_name.upper().replace(' ', '-')}-5LB-WB",
                    "inventory_quantity": 25,
                    "weight": 2.27,
                    "weight_unit": "kg",
                    "inventory_management": "shopify",
                    "inventory_policy": "deny"
                }
            ]

            # Prepare product data
            product_data = {
                "product": {
                    "title": product_title,
                    "body_html": description,
                    "vendor": "Big River Coffee",
                    "product_type": "Coffee",
                    "status": self.template_status_var.get(),
                    "tags": f"coffee, {roast_level.lower().replace(' ', '-')}, {origin.lower()}, arabica, premium, big-river-coffee",
                    "variants": variants
                }
            }

            # Show creating message
            self.status_var.set(f"Creating {product_title}...")
            template_window.destroy()  # Close the form window

            # Create product in background thread
            threading.Thread(target=self._create_product_thread,
                           args=(product_data,), daemon=True).start()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to create coffee product: {str(e)}")

    def upload_product_image(self):
        """Upload a product image for AI analysis"""
        try:
            # Use the stored template window reference
            parent_window = getattr(self, 'current_template_window', self.root)

            file_path = filedialog.askopenfilename(
                title="Select Product Image",
                parent=parent_window,  # Set parent to keep template window open
                filetypes=[
                    ("Image files", "*.jpg *.jpeg *.png *.gif *.bmp"),
                    ("All files", "*.*")
                ]
            )

            if file_path:
                self.uploaded_image_path = file_path
                filename = os.path.basename(file_path)
                self.image_status_label.config(text=f"Selected: {filename}")

                # Bring the template window back to front
                if hasattr(self, 'current_template_window') and self.current_template_window.winfo_exists():
                    self.current_template_window.lift()
                    self.current_template_window.focus_force()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to upload image: {str(e)}")

    def close_template_window(self):
        """Clean up and close the template window"""
        try:
            # Clean up references
            if hasattr(self, 'current_template_window'):
                self.current_template_window.destroy()
                delattr(self, 'current_template_window')

            # Clean up image references
            self.uploaded_image_path = None

        except Exception as e:
            print(f"Error closing template window: {e}")

    def analyze_image_with_ai(self):
        """Analyze the uploaded image with Gemini AI to extract product information"""
        if not self.uploaded_image_path:
            messagebox.showwarning("No Image", "Please upload an image first.")
            return

        self.status_var.set("Analyzing image with AI...")
        threading.Thread(target=self._analyze_image_thread, daemon=True).start()

    def _analyze_image_thread(self):
        """Background thread for AI image analysis"""
        try:
            # Read and encode the image
            with open(self.uploaded_image_path, 'rb') as image_file:
                image_data = base64.b64encode(image_file.read()).decode('utf-8')

            # Prepare the AI prompt
            prompt = """Analyze this coffee product image and extract the following information for a professional coffee product description:

1. ROAST_LEVEL: Determine the roast level (Light Roast, Medium Roast, or Dark Roast) based on bean color
2. ORIGIN: Identify the origin/region if visible on packaging, or suggest a likely origin
3. FLAVOR_PROFILE: Describe the expected flavor profile based on roast level and visual cues
4. BEAN_TYPE: Identify if it's Arabica, Robusta, or a blend (default to 100% Arabica for premium coffee)
5. PROCESSING_METHOD: Suggest processing method (Washed, Natural, Honey, etc.)
6. ALTITUDE: Suggest growing altitude if origin is identified
7. HARVEST_SEASON: Suggest harvest season based on origin
8. BREWING_RECOMMENDATIONS: Provide specific brewing recommendations
9. DETAILED_TASTING_NOTES: Provide detailed tasting notes

Please respond in this exact format:
ROAST_LEVEL: [value]
ORIGIN: [value]
FLAVOR_PROFILE: [value]
BEAN_TYPE: [value]
PROCESSING_METHOD: [value]
ALTITUDE: [value]
HARVEST_SEASON: [value]
BREWING_RECOMMENDATIONS: [value]
DETAILED_TASTING_NOTES: [value]

Focus on creating professional, marketing-friendly descriptions suitable for a premium coffee brand."""

            # Prepare the API request
            headers = {
                'Content-Type': 'application/json',
            }

            data = {
                "contents": [{
                    "parts": [
                        {"text": prompt},
                        {
                            "inline_data": {
                                "mime_type": "image/jpeg",
                                "data": image_data
                            }
                        }
                    ]
                }]
            }

            # Make the API request
            response = requests.post(
                f"{self.gemini_url}?key={self.gemini_api_key}",
                headers=headers,
                json=data
            )

            if response.status_code == 200:
                result = response.json()
                ai_text = result['candidates'][0]['content']['parts'][0]['text']

                # Parse the AI response and update the description
                self.root.after(0, lambda: self._update_description_with_ai_data(ai_text))
                self.root.after(0, lambda: self.status_var.set("AI analysis complete!"))
            else:
                error_msg = f"AI analysis failed: {response.status_code}"
                self.root.after(0, lambda: self.status_var.set(error_msg))
                self.root.after(0, lambda: messagebox.showerror("AI Error", error_msg))

        except Exception as e:
            error_msg = f"Error during AI analysis: {str(e)}"
            self.root.after(0, lambda: self.status_var.set(error_msg))
            self.root.after(0, lambda: messagebox.showerror("Error", error_msg))

    def _update_description_with_ai_data(self, ai_response):
        """Update the description template with AI-extracted data"""
        try:
            # Parse the AI response
            ai_data = {}
            for line in ai_response.split('\n'):
                if ':' in line:
                    key, value = line.split(':', 1)
                    ai_data[key.strip()] = value.strip()

            # Update the description preview with AI data
            coffee_name = self.coffee_name_entry.get().strip() or "[Coffee Name]"
            roast_level = ai_data.get('ROAST_LEVEL', self.roast_level_var.get())

            # Update form fields if AI provided better data
            if 'ROAST_LEVEL' in ai_data and ai_data['ROAST_LEVEL'] in ["Light Roast", "Medium Roast", "Dark Roast"]:
                self.roast_level_var.set(ai_data['ROAST_LEVEL'])

            if 'ORIGIN' in ai_data:
                self.origin_entry.delete(0, tk.END)
                self.origin_entry.insert(0, ai_data['ORIGIN'])

            if 'FLAVOR_PROFILE' in ai_data:
                self.flavor_entry.delete(0, tk.END)
                self.flavor_entry.insert(0, ai_data['FLAVOR_PROFILE'])

            # Generate updated description with AI data
            description = self._generate_ai_enhanced_description(coffee_name, ai_data)

            self.desc_preview.delete(1.0, tk.END)
            self.desc_preview.insert(1.0, description)

            messagebox.showinfo("AI Analysis Complete",
                              "Product information has been analyzed and updated! Review the description preview.")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to process AI response: {str(e)}")

    def _generate_ai_enhanced_description(self, name, ai_data):
        """Generate description with AI-enhanced data"""
        roast_level = ai_data.get('ROAST_LEVEL', 'Medium Roast')
        origin = ai_data.get('ORIGIN', 'Colombian')
        flavor_profile = ai_data.get('FLAVOR_PROFILE', 'Rich and smooth')

        description = f"""<h2>{name} - {roast_level}</h2>

<p><strong>Experience the exceptional quality of Big River Coffee's {name}</strong></p>

<p>Our {name} is a carefully crafted {roast_level.lower()} featuring premium {origin} beans. This exceptional coffee delivers {flavor_profile.lower()}, making it perfect for any time of day.</p>

<h3>Product Details:</h3>
<ul>
<li><strong>Roast Level:</strong> {ai_data.get('ROAST_LEVEL', '[ROAST_LEVEL]')}</li>
<li><strong>Origin:</strong> {ai_data.get('ORIGIN', '[ORIGIN]')}</li>
<li><strong>Flavor Profile:</strong> {ai_data.get('FLAVOR_PROFILE', '[FLAVOR_PROFILE]')}</li>
<li><strong>Bean Type:</strong> {ai_data.get('BEAN_TYPE', '[BEAN_TYPE]')}</li>
<li><strong>Processing:</strong> {ai_data.get('PROCESSING_METHOD', '[PROCESSING_METHOD]')}</li>
<li><strong>Altitude:</strong> {ai_data.get('ALTITUDE', '[ALTITUDE]')}</li>
<li><strong>Harvest Season:</strong> {ai_data.get('HARVEST_SEASON', '[HARVEST_SEASON]')}</li>
</ul>

<h3>Available Options:</h3>
<ul>
<li>12oz Whole Bean or Ground</li>
<li>2lb Whole Bean or Ground</li>
<li>5lb Whole Bean</li>
</ul>

<p><strong>Brewing Recommendations:</strong><br>
{ai_data.get('BREWING_RECOMMENDATIONS', '[BREWING_RECOMMENDATIONS]')}</p>

<p><strong>Tasting Notes:</strong><br>
{ai_data.get('DETAILED_TASTING_NOTES', '[DETAILED_TASTING_NOTES]')}</p>

<p><em>Roasted fresh to order by Big River Coffee. Each bag is carefully sealed to preserve peak flavor and aroma.</em></p>"""

        return description


def main():
    root = tk.Tk()
    app = ShopifyDesktopApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
