import React from 'react'
import { <PERSON><PERSON>, <PERSON>, User, RefreshCw } from 'lucide-react'
import { useShopify } from '../context/ShopifyContext'

export default function Header({ setSidebarOpen }) {
  const { refreshProducts, loading, shopInfo } = useShopify()

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="flex items-center justify-between h-16 px-6">
        <div className="flex items-center">
          <button
            onClick={() => setSidebarOpen(true)}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
          >
            <Menu className="h-6 w-6" />
          </button>
          
          <div className="ml-4 lg:ml-0">
            <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <button
            onClick={refreshProducts}
            disabled={loading}
            className={`
              p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 transition-colors duration-200
              ${loading ? 'animate-spin' : ''}
            `}
            title="Refresh data"
          >
            <RefreshCw className="h-5 w-5" />
          </button>

          <button className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 relative">
            <Bell className="h-5 w-5" />
            <span className="absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full"></span>
          </button>

          <div className="flex items-center space-x-3">
            <div className="text-right">
              <p className="text-sm font-medium text-gray-900">Admin User</p>
              <p className="text-xs text-gray-500">{shopInfo?.name || 'Big River Coffee'}</p>
            </div>
            <div className="h-8 w-8 bg-coffee-600 rounded-full flex items-center justify-center">
              <User className="h-4 w-4 text-white" />
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
