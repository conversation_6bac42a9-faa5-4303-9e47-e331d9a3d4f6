const axios = require('axios');
const config = require('./config');

class ShopifyAPI {
  constructor() {
    this.config = config;
    this.headers = {
      'X-Shopify-Access-Token': this.config.accessToken,
      'Content-Type': 'application/json'
    };
  }

  // GraphQL query method
  async graphqlQuery(query, variables = {}) {
    try {
      const response = await axios.post(this.config.graphqlEndpoint, {
        query,
        variables
      }, {
        headers: this.headers
      });

      if (response.data.errors) {
        console.error('GraphQL Errors:', response.data.errors);
        throw new Error('GraphQL query failed');
      }

      return response.data;
    } catch (error) {
      console.error('API Request Error:', error.response?.data || error.message);
      throw error;
    }
  }

  // Get all products with detailed information
  async getProducts(first = 10) {
    const query = `
      query getProducts($first: Int!) {
        products(first: $first) {
          edges {
            node {
              id
              title
              handle
              description
              productType
              vendor
              status
              createdAt
              updatedAt
              tags
              images(first: 5) {
                edges {
                  node {
                    id
                    url
                    altText
                  }
                }
              }
              variants(first: 10) {
                edges {
                  node {
                    id
                    title
                    price
                    compareAtPrice
                    sku
                    inventoryQuantity

                  }
                }
              }
            }
          }
          pageInfo {
            hasNextPage
            hasPreviousPage
          }
        }
      }
    `;

    return await this.graphqlQuery(query, { first });
  }

  // Search for products by title (useful for finding cowboy coffee)
  async searchProducts(searchTerm, first = 10) {
    const query = `
      query searchProducts($query: String!, $first: Int!) {
        products(first: $first, query: $query) {
          edges {
            node {
              id
              title
              handle
              description
              productType
              vendor
              status
              tags
              images(first: 3) {
                edges {
                  node {
                    id
                    url
                    altText
                  }
                }
              }
              variants(first: 5) {
                edges {
                  node {
                    id
                    title
                    price
                    compareAtPrice
                    sku
                    inventoryQuantity
                  }
                }
              }
            }
          }
        }
      }
    `;

    return await this.graphqlQuery(query, { 
      query: `title:*${searchTerm}*`,
      first 
    });
  }

  // Get a specific product by ID
  async getProduct(productId) {
    const query = `
      query getProduct($id: ID!) {
        product(id: $id) {
          id
          title
          handle
          description
          productType
          vendor
          status
          createdAt
          updatedAt
          tags
          images(first: 10) {
            edges {
              node {
                id
                url
                altText
              }
            }
          }
          variants(first: 20) {
            edges {
              node {
                id
                title
                price
                compareAtPrice
                sku
                inventoryQuantity

                selectedOptions {
                  name
                  value
                }
              }
            }
          }
          options {
            id
            name
            values
          }
        }
      }
    `;

    return await this.graphqlQuery(query, { id: productId });
  }

  // Get all selling plans
  async getSellingPlans(first = 10) {
    const query = `
      query getSellingPlans($first: Int!) {
        sellingPlanGroups(first: $first) {
          edges {
            node {
              id
              name
              merchantCode
              description
              options
              position
              summary
              createdAt
              sellingPlans(first: 10) {
                edges {
                  node {
                    id
                    name
                    description
                    options
                    position
                    billingPolicy {
                      ... on SellingPlanRecurringBillingPolicy {
                        interval
                        intervalCount
                        minCycles
                        maxCycles
                      }
                    }
                    deliveryPolicy {
                      ... on SellingPlanRecurringDeliveryPolicy {
                        interval
                        intervalCount
                      }
                    }
                    pricingPolicies {
                      ... on SellingPlanFixedPricingPolicy {
                        adjustmentType
                        adjustmentValue {
                          ... on SellingPlanPricingPolicyPercentageValue {
                            percentage
                          }
                          ... on MoneyV2 {
                            amount
                            currencyCode
                          }
                        }
                      }
                    }
                  }
                }
              }
              productVariants(first: 20) {
                edges {
                  node {
                    id
                    title
                    product {
                      id
                      title
                    }
                  }
                }
              }
            }
          }
        }
      }
    `;

    return await this.graphqlQuery(query, { first });
  }

  // Get product with selling plans
  async getProductWithSellingPlans(productId) {
    const query = `
      query getProductWithSellingPlans($id: ID!) {
        product(id: $id) {
          id
          title
          handle
          description
          sellingPlanGroups(first: 10) {
            edges {
              node {
                id
                name
                merchantCode
                description
                sellingPlans(first: 10) {
                  edges {
                    node {
                      id
                      name
                      description
                      billingPolicy {
                        ... on SellingPlanRecurringBillingPolicy {
                          interval
                          intervalCount
                        }
                      }
                      deliveryPolicy {
                        ... on SellingPlanRecurringDeliveryPolicy {
                          interval
                          intervalCount
                        }
                      }
                      pricingPolicies {
                        ... on SellingPlanFixedPricingPolicy {
                          adjustmentType
                          adjustmentValue {
                            ... on SellingPlanPricingPolicyPercentageValue {
                              percentage
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
          variants(first: 20) {
            edges {
              node {
                id
                title
                price

              }
            }
          }
        }
      }
    `;

    return await this.graphqlQuery(query, { id: productId });
  }

  // Create a new selling plan group
  async createSellingPlanGroup(name, merchantCode, description, sellingPlans) {
    const mutation = `
      mutation sellingPlanGroupCreate($input: SellingPlanGroupInput!) {
        sellingPlanGroupCreate(input: $input) {
          sellingPlanGroup {
            id
            name
            merchantCode
            description
            sellingPlans(first: 10) {
              edges {
                node {
                  id
                  name
                  description
                }
              }
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const input = {
      name,
      merchantCode,
      description,
      sellingPlans
    };

    return await this.graphqlQuery(mutation, { input });
  }

  // Add product to selling plan group
  async addProductToSellingPlanGroup(sellingPlanGroupId, productId) {
    const mutation = `
      mutation sellingPlanGroupAddProducts($id: ID!, $productIds: [ID!]!) {
        sellingPlanGroupAddProducts(id: $id, productIds: $productIds) {
          sellingPlanGroup {
            id
            name
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    return await this.graphqlQuery(mutation, {
      id: sellingPlanGroupId,
      productIds: [productId]
    });
  }

  // Create a product with selling plans
  async createProductWithSellingPlans(productData, sellingPlanGroupIds = []) {
    // First create the product
    const productMutation = `
      mutation productCreate($input: ProductInput!) {
        productCreate(input: $input) {
          product {
            id
            title
            handle
            status
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const productResult = await this.graphqlQuery(productMutation, { input: productData });

    if (productResult.data.productCreate.userErrors.length > 0) {
      throw new Error(`Product creation failed: ${productResult.data.productCreate.userErrors.map(e => e.message).join(', ')}`);
    }

    const productId = productResult.data.productCreate.product.id;

    // Add to selling plan groups if provided
    if (sellingPlanGroupIds.length > 0) {
      for (const groupId of sellingPlanGroupIds) {
        await this.addProductToSellingPlanGroup(groupId, productId);
      }
    }

    return {
      product: productResult.data.productCreate.product,
      productId
    };
  }

  // Create product using REST API (more reliable for product creation)
  async createProductREST(productData) {
    const endpoint = `${this.config.restEndpoint}/products.json`;

    try {
      const response = await axios.post(endpoint, {
        product: productData
      }, {
        headers: this.headers
      });

      return response.data;
    } catch (error) {
      console.error('REST API Request Error:', error.response?.data || error.message);
      throw error;
    }
  }

  // Get shop information
  async getShopInfo() {
    try {
      const query = `
        query {
          shop {
            name
            email
            myshopifyDomain
            currencyCode
            plan {
              displayName
            }
          }
        }
      `;

      const result = await this.graphqlQuery(query);
      return result.data.shop;
    } catch (error) {
      throw new Error(`Failed to get shop info: ${error.message}`);
    }
  }

  // Get orders using REST API (GraphQL orders are more complex)
  async getOrders(limit = 50) {
    try {
      const endpoint = `${this.config.restEndpoint}/orders.json`;
      const response = await axios.get(endpoint, {
        headers: this.headers,
        params: {
          limit: limit,
          status: 'any'
        }
      });

      return response.data.orders;
    } catch (error) {
      throw new Error(`Failed to get orders: ${error.message}`);
    }
  }

  // Update product status using REST API
  async updateProductStatus(productId, status) {
    try {
      const endpoint = `${this.config.restEndpoint}/products/${productId}.json`;
      const response = await axios.put(endpoint, {
        product: {
          id: productId,
          status: status
        }
      }, {
        headers: this.headers
      });

      return response.data.product;
    } catch (error) {
      throw new Error(`Failed to update product status: ${error.message}`);
    }
  }

  // Test API connection
  async testConnection() {
    try {
      const query = `
        query {
          shop {
            name
            email
            myshopifyDomain
            currencyCode
          }
        }
      `;

      const result = await this.graphqlQuery(query);
      return result.data.shop;
    } catch (error) {
      throw new Error(`API connection test failed: ${error.message}`);
    }
  }
}

module.exports = ShopifyAPI;
