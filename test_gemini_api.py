#!/usr/bin/env python3
"""
Quick test to check if Gemini API is responding
"""

import requests
import json

def test_gemini_api():
    """Test the Gemini API with a simple text prompt"""
    
    # API configuration
    api_key = 'AIzaSyD9DKZ0zV_OWko4hqo15azaEL8A6jHC3-c'
    url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent'
    
    # Simple test prompt
    headers = {
        'Content-Type': 'application/json',
    }
    
    data = {
        "contents": [{
            "parts": [
                {"text": "Hello, can you respond with 'API is working' to test the connection?"}
            ]
        }]
    }
    
    try:
        print("Testing Gemini API connection...")
        print(f"URL: {url}")
        print(f"API Key: {api_key[:20]}...")
        
        response = requests.post(
            f"{url}?key={api_key}",
            headers=headers,
            json=data,
            timeout=30
        )
        
        print(f"Response Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API Response Success!")
            print(f"Response: {json.dumps(result, indent=2)}")
            
            if 'candidates' in result and len(result['candidates']) > 0:
                ai_text = result['candidates'][0]['content']['parts'][0]['text']
                print(f"✅ AI Response Text: {ai_text}")
                return True
            else:
                print("❌ No candidates in response")
                return False
                
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Error Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_gemini_with_image_analysis():
    """Test the Gemini API with image analysis prompt (no actual image)"""
    
    api_key = 'AIzaSyD9DKZ0zV_OWko4hqo15azaEL8A6jHC3-c'
    url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent'
    
    # Test the coffee analysis prompt format
    prompt = """Analyze a coffee product and provide the following information:

1. ROAST_LEVEL: Medium Roast
2. ORIGIN: Colombian
3. FLAVOR_PROFILE: Rich, smooth, with notes of chocolate and caramel
4. BEAN_TYPE: 100% Arabica
5. PROCESSING_METHOD: Washed
6. ALTITUDE: 1200-1800m
7. HARVEST_SEASON: October-February
8. BREWING_RECOMMENDATIONS: Use 1:15 ratio, medium grind, 200°F water
9. DETAILED_TASTING_NOTES: Full-bodied with chocolate undertones and caramel sweetness

Please respond in this exact format:
ROAST_LEVEL: [value]
ORIGIN: [value]
FLAVOR_PROFILE: [value]
BEAN_TYPE: [value]
PROCESSING_METHOD: [value]
ALTITUDE: [value]
HARVEST_SEASON: [value]
BREWING_RECOMMENDATIONS: [value]
DETAILED_TASTING_NOTES: [value]"""

    headers = {
        'Content-Type': 'application/json',
    }
    
    data = {
        "contents": [{
            "parts": [
                {"text": prompt}
            ]
        }]
    }
    
    try:
        print("\nTesting coffee analysis prompt format...")
        
        response = requests.post(
            f"{url}?key={api_key}",
            headers=headers,
            json=data,
            timeout=30
        )
        
        print(f"Response Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Coffee Analysis Test Success!")
            
            if 'candidates' in result and len(result['candidates']) > 0:
                ai_text = result['candidates'][0]['content']['parts'][0]['text']
                print(f"✅ AI Coffee Analysis Response:\n{ai_text}")
                
                # Test parsing
                ai_data = {}
                for line in ai_text.split('\n'):
                    if ':' in line:
                        key, value = line.split(':', 1)
                        ai_data[key.strip()] = value.strip()
                
                print(f"✅ Parsed Data: {json.dumps(ai_data, indent=2)}")
                return True
            else:
                print("❌ No candidates in coffee analysis response")
                return False
                
        else:
            print(f"❌ Coffee Analysis API Error: {response.status_code}")
            print(f"Error Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Coffee analysis error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Gemini API Test Suite")
    print("=" * 50)
    
    # Test 1: Basic API connection
    basic_test = test_gemini_api()
    
    # Test 2: Coffee analysis format
    if basic_test:
        coffee_test = test_gemini_with_image_analysis()
        
        if coffee_test:
            print("\n✅ All tests passed! Gemini API is working correctly.")
        else:
            print("\n❌ Coffee analysis test failed.")
    else:
        print("\n❌ Basic API test failed. Check your API key and connection.")
