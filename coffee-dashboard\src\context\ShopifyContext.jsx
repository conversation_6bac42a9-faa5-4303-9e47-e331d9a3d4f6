import React, { createContext, useContext, useState, useEffect } from 'react'
import shopifyApiService from '../services/shopifyApi'

const ShopifyContext = createContext()

export const useShopify = () => {
  const context = useContext(ShopifyContext)
  if (!context) {
    throw new Error('useShopify must be used within a ShopifyProvider')
  }
  return context
}

// Mock data for development - replace with actual API calls
const mockProducts = [
  {
    id: '10048597295419',
    title: 'Cowboy Coffee - Dark Roast',
    handle: 'cowboy-coffee',
    status: 'active',
    vendor: 'Big River Coffee',
    product_type: 'Coffee',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
    variants: [
      { id: '1', title: '12oz / whole bean', price: '19.99', inventory_quantity: 100 },
      { id: '2', title: '12oz / ground', price: '19.99', inventory_quantity: 95 },
      { id: '3', title: '2lb / whole bean', price: '31.99', inventory_quantity: 50 },
      { id: '4', title: '2lb / ground', price: '31.99', inventory_quantity: 45 },
      { id: '5', title: '5lb / whole bean', price: '66.99', inventory_quantity: 25 }
    ],
    tags: ['coffee', 'dark-roast', 'colombian', 'arabica', 'premium'],
    images: [
      { src: 'https://via.placeholder.com/300x300/8B4513/FFFFFF?text=Cowboy+Coffee', alt: 'Cowboy Coffee Mockup' },
      { src: 'https://via.placeholder.com/300x300/D2691E/FFFFFF?text=Label', alt: 'Cowboy Coffee Label' }
    ]
  },
  {
    id: '10129490968891',
    title: 'Controlled Burn - Dark Roast',
    handle: 'controlled-burn',
    status: 'draft',
    vendor: 'Big River Coffee',
    product_type: 'Coffee',
    created_at: '2024-01-20T14:30:00Z',
    updated_at: '2024-01-20T14:30:00Z',
    variants: [
      { id: '6', title: '12oz / whole bean', price: '19.99', inventory_quantity: 100 },
      { id: '7', title: '12oz / ground', price: '19.99', inventory_quantity: 100 },
      { id: '8', title: '2lb / whole bean', price: '31.99', inventory_quantity: 50 },
      { id: '9', title: '2lb / ground', price: '31.99', inventory_quantity: 50 },
      { id: '10', title: '5lb / whole bean', price: '66.99', inventory_quantity: 25 }
    ],
    tags: ['coffee', 'dark-roast', 'colombian', 'arabica', 'premium'],
    images: []
  },
  {
    id: '10129491034427',
    title: 'Fairway Fuel - Medium Roast',
    handle: 'fairway-fuel',
    status: 'draft',
    vendor: 'Big River Coffee',
    product_type: 'Coffee',
    created_at: '2024-01-20T14:32:00Z',
    updated_at: '2024-01-20T14:32:00Z',
    variants: [
      { id: '11', title: '12oz / whole bean', price: '19.99', inventory_quantity: 100 },
      { id: '12', title: '12oz / ground', price: '19.99', inventory_quantity: 100 },
      { id: '13', title: '2lb / whole bean', price: '31.99', inventory_quantity: 50 },
      { id: '14', title: '2lb / ground', price: '31.99', inventory_quantity: 50 },
      { id: '15', title: '5lb / whole bean', price: '66.99', inventory_quantity: 25 }
    ],
    tags: ['coffee', 'medium-roast', 'colombian', 'arabica', 'premium'],
    images: []
  }
]

const mockAnalytics = {
  totalProducts: 9,
  activeProducts: 1,
  draftProducts: 8,
  totalRevenue: 15420.50,
  monthlyGrowth: 12.5,
  topSellingProducts: [
    { name: 'Cowboy Coffee', sales: 145, revenue: 2899.55 },
    { name: 'Morning Mist', sales: 89, revenue: 1778.11 },
    { name: 'Fairway Fuel', sales: 67, revenue: 1338.33 }
  ],
  salesData: [
    { month: 'Jan', sales: 1200, revenue: 24000 },
    { month: 'Feb', sales: 1350, revenue: 27000 },
    { month: 'Mar', sales: 1100, revenue: 22000 },
    { month: 'Apr', sales: 1450, revenue: 29000 },
    { month: 'May', sales: 1600, revenue: 32000 },
    { month: 'Jun', sales: 1750, revenue: 35000 }
  ]
}

export const ShopifyProvider = ({ children }) => {
  const [products, setProducts] = useState([])
  const [analytics, setAnalytics] = useState(null)
  const [shopInfo, setShopInfo] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Load real data from Shopify API
  useEffect(() => {
    loadInitialData()
  }, [])

  const loadInitialData = async () => {
    setLoading(true)
    setError(null)

    try {
      // Load shop info, products, and analytics in parallel
      const [shopData, productsData, analyticsData] = await Promise.all([
        shopifyApiService.getShopInfo(),
        shopifyApiService.getProducts(50),
        shopifyApiService.getAnalytics()
      ])

      setShopInfo(shopData)
      setProducts(productsData)
      setAnalytics(analyticsData)
    } catch (err) {
      console.error('Error loading data:', err)
      setError(err.message)
      // Fallback to mock data if API fails
      setProducts(mockProducts)
      setAnalytics(mockAnalytics)
    } finally {
      setLoading(false)
    }
  }

  const refreshProducts = async () => {
    setLoading(true)
    setError(null)

    try {
      const [productsData, analyticsData] = await Promise.all([
        shopifyApiService.getProducts(50),
        shopifyApiService.getAnalytics()
      ])

      setProducts(productsData)
      setAnalytics(analyticsData)
    } catch (err) {
      console.error('Error refreshing data:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const updateProductStatus = async (productId, status) => {
    setLoading(true)
    setError(null)

    try {
      // Extract numeric ID from GraphQL ID
      const numericId = productId.replace('gid://shopify/Product/', '')

      await shopifyApiService.updateProductStatus(numericId, status)

      // Update local state
      setProducts(prev => prev.map(product =>
        product.id === productId ? { ...product, status: status.toUpperCase() } : product
      ))
    } catch (err) {
      console.error('Error updating product status:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const createProduct = async (productData) => {
    setLoading(true)
    try {
      // Simulate API call
      setTimeout(() => {
        const newProduct = {
          id: Date.now().toString(),
          ...productData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          variants: productData.variants || []
        }
        setProducts(prev => [...prev, newProduct])
        setLoading(false)
      }, 1000)
    } catch (err) {
      setError(err.message)
      setLoading(false)
    }
  }

  const value = {
    products,
    analytics,
    shopInfo,
    loading,
    error,
    refreshProducts,
    updateProductStatus,
    createProduct,
    loadInitialData
  }

  return (
    <ShopifyContext.Provider value={value}>
      {children}
    </ShopifyContext.Provider>
  )
}
