import React from 'react'
import {
  Package,
  DollarSign,
  TrendingUp,
  Users,
  Coffee,
  Eye,
  Edit
} from 'lucide-react'
import { useShopify } from '../context/ShopifyContext'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts'

export default function Dashboard() {
  const { products, analytics, loading, error, shopInfo } = useShopify()

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-coffee-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your coffee business data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <Coffee className="h-12 w-12 mx-auto mb-2" />
            <p className="text-lg font-medium">Unable to load data</p>
            <p className="text-sm text-gray-600">{error}</p>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="btn-primary"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  const stats = [
    {
      name: 'Total Products',
      value: analytics?.totalProducts || 0,
      icon: Package,
      color: 'bg-blue-500',
      change: '+2 this month'
    },
    {
      name: 'Active Products',
      value: analytics?.activeProducts || 0,
      icon: Coffee,
      color: 'bg-green-500',
      change: `${analytics?.draftProducts || 0} drafts`
    },
    {
      name: 'Monthly Revenue',
      value: `$${analytics?.totalRevenue?.toLocaleString() || '0'}`,
      icon: DollarSign,
      color: 'bg-yellow-500',
      change: `+${analytics?.monthlyGrowth || 0}%`
    },
    {
      name: 'Subscription Orders',
      value: '127',
      icon: Users,
      color: 'bg-purple-500',
      change: '+12% this week'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-coffee-600 to-coffee-800 rounded-lg p-6 text-white">
        <h1 className="text-3xl font-bold mb-2">Welcome to {shopInfo?.name || 'Big River Coffee'} Dashboard</h1>
        <p className="text-coffee-100">Manage your coffee products, track sales, and grow your business.</p>
        {shopInfo && (
          <div className="mt-4 text-sm text-coffee-200">
            <p>Store: {shopInfo.myshopifyDomain} • Currency: {shopInfo.currencyCode}</p>
          </div>
        )}
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="flex items-center">
              <div className={`${stat.color} p-3 rounded-lg`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                <p className="text-sm text-gray-500">{stat.change}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Chart */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Sales Overview</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={analytics?.salesData || []}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line 
                type="monotone" 
                dataKey="revenue" 
                stroke="#8B4513" 
                strokeWidth={2}
                dot={{ fill: '#8B4513' }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Top Products */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Selling Products</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={analytics?.topSellingProducts || []}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="sales" fill="#8B4513" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Recent Products */}
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Recent Products</h3>
          <button className="btn-primary">View All Products</button>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Variants
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {products.slice(0, 5).map((product) => (
                <tr key={product.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-lg bg-coffee-100 flex items-center justify-center">
                          <Coffee className="h-5 w-5 text-coffee-600" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{product.title}</div>
                        <div className="text-sm text-gray-500">{product.handle}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`status-badge ${
                      product.status === 'ACTIVE' || product.status === 'active' ? 'status-active' :
                      product.status === 'DRAFT' || product.status === 'draft' ? 'status-draft' : 'status-archived'
                    }`}>
                      {product.status?.toLowerCase() || 'draft'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {product.variants?.length || 0} variants
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(product.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button className="text-coffee-600 hover:text-coffee-900">
                        <Eye className="h-4 w-4" />
                      </button>
                      <button className="text-gray-600 hover:text-gray-900">
                        <Edit className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
