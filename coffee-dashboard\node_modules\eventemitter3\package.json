{"name": "eventemitter3", "version": "4.0.7", "description": "EventEmitter3 focuses on performance while maintaining a Node.js AND browser compatible interface.", "main": "index.js", "typings": "index.d.ts", "scripts": {"browserify": "rm -rf umd && mkdir umd && browserify index.js -s EventEmitter3 -o umd/eventemitter3.js", "minify": "uglifyjs umd/eventemitter3.js --source-map -cm -o umd/eventemitter3.min.js", "benchmark": "find benchmarks/run -name '*.js' -exec benchmarks/start.sh {} \\;", "test": "nyc --reporter=html --reporter=text mocha test/test.js", "prepublishOnly": "npm run browserify && npm run minify", "test-browser": "node test/browser.js"}, "files": ["index.js", "index.d.ts", "umd"], "repository": {"type": "git", "url": "git://github.com/primus/eventemitter3.git"}, "keywords": ["EventEmitter", "EventEmitter2", "EventEmitter3", "Events", "addEventListener", "addListener", "emit", "emits", "emitter", "event", "once", "pub/sub", "publish", "reactor", "subscribe"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/primus/eventemitter3/issues"}, "devDependencies": {"assume": "^2.2.0", "browserify": "^16.5.0", "mocha": "^8.0.1", "nyc": "^15.1.0", "pre-commit": "^1.2.0", "sauce-browsers": "^2.0.0", "sauce-test": "^1.3.3", "uglify-js": "^3.9.0"}}