{"name": "shopify-product-manager", "version": "1.0.0", "description": "Shopify Admin API product management tool", "main": "index.js", "scripts": {"start": "node index.js", "test": "node test-api.js"}, "dependencies": {"@tailwindcss/postcss": "^4.1.11", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^5.1.0", "lucide-react": "^0.525.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "recharts": "^3.0.2", "tailwindcss": "^4.1.11", "vite": "^7.0.3"}, "keywords": ["shopify", "api", "products"], "author": "", "license": "ISC"}