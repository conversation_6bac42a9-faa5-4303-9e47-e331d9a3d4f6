const express = require('express');
const cors = require('cors');

const app = express();
const port = 3001;

// Enable CORS for frontend
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));

app.use(express.json());

// Mock data for development (avoiding rate limits and API issues)
const mockShopInfo = {
  name: 'Big River Coffee',
  email: '<EMAIL>',
  myshopifyDomain: '9bc094.myshopify.com',
  currencyCode: 'USD',
  plan: { displayName: 'Shopify Plus' }
};

const mockProducts = [
  {
    id: 'gid://shopify/Product/10048597295419',
    title: 'Cowboy Coffee - Dark Roast',
    handle: 'cowboy-coffee',
    status: 'ACTIVE',
    vendor: 'Big River Coffee',
    productType: 'Coffee',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    variants: [
      { id: '1', title: '12oz / whole bean', price: '19.99', inventoryQuantity: 100 },
      { id: '2', title: '12oz / ground', price: '19.99', inventoryQuantity: 95 },
      { id: '3', title: '2lb / whole bean', price: '31.99', inventoryQuantity: 50 },
      { id: '4', title: '2lb / ground', price: '31.99', inventoryQuantity: 45 },
      { id: '5', title: '5lb / whole bean', price: '66.99', inventoryQuantity: 25 }
    ],
    tags: ['coffee', 'dark-roast', 'colombian', 'arabica', 'premium'],
    images: [
      { src: 'https://via.placeholder.com/300x300/8B4513/FFFFFF?text=Cowboy+Coffee', alt: 'Cowboy Coffee Mockup' },
      { src: 'https://via.placeholder.com/300x300/D2691E/FFFFFF?text=Label', alt: 'Cowboy Coffee Label' }
    ]
  },
  {
    id: 'gid://shopify/Product/10129490968891',
    title: 'Controlled Burn - Dark Roast',
    handle: 'controlled-burn',
    status: 'DRAFT',
    vendor: 'Big River Coffee',
    productType: 'Coffee',
    createdAt: '2024-01-20T14:30:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
    variants: [
      { id: '6', title: '12oz / whole bean', price: '19.99', inventoryQuantity: 100 },
      { id: '7', title: '12oz / ground', price: '19.99', inventoryQuantity: 100 },
      { id: '8', title: '2lb / whole bean', price: '31.99', inventoryQuantity: 50 },
      { id: '9', title: '2lb / ground', price: '31.99', inventoryQuantity: 50 },
      { id: '10', title: '5lb / whole bean', price: '66.99', inventoryQuantity: 25 }
    ],
    tags: ['coffee', 'dark-roast', 'colombian', 'arabica', 'premium'],
    images: []
  },
  {
    id: 'gid://shopify/Product/10129491034427',
    title: 'Fairway Fuel - Medium Roast',
    handle: 'fairway-fuel',
    status: 'DRAFT',
    vendor: 'Big River Coffee',
    productType: 'Coffee',
    createdAt: '2024-01-20T14:32:00Z',
    updatedAt: '2024-01-20T14:32:00Z',
    variants: [
      { id: '11', title: '12oz / whole bean', price: '19.99', inventoryQuantity: 100 },
      { id: '12', title: '12oz / ground', price: '19.99', inventoryQuantity: 100 },
      { id: '13', title: '2lb / whole bean', price: '31.99', inventoryQuantity: 50 },
      { id: '14', title: '2lb / ground', price: '31.99', inventoryQuantity: 50 },
      { id: '15', title: '5lb / whole bean', price: '66.99', inventoryQuantity: 25 }
    ],
    tags: ['coffee', 'medium-roast', 'colombian', 'arabica', 'premium'],
    images: []
  }
];

const mockAnalytics = {
  totalProducts: 9,
  activeProducts: 1,
  draftProducts: 8,
  totalRevenue: 15420.50,
  monthlyGrowth: 12.5,
  topSellingProducts: [
    { name: 'Cowboy Coffee', sales: 145, revenue: 2899.55 },
    { name: 'Morning Mist', sales: 89, revenue: 1778.11 },
    { name: 'Fairway Fuel', sales: 67, revenue: 1338.33 }
  ],
  salesData: [
    { month: 'Jan', sales: 1200, revenue: 24000 },
    { month: 'Feb', sales: 1350, revenue: 27000 },
    { month: 'Mar', sales: 1100, revenue: 22000 },
    { month: 'Apr', sales: 1450, revenue: 29000 },
    { month: 'May', sales: 1600, revenue: 32000 },
    { month: 'Jun', sales: 1750, revenue: 35000 }
  ]
};

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Big River Coffee - Shopify API Server',
    status: 'Running',
    endpoints: {
      health: '/health',
      shop: '/api/shop',
      products: '/api/products',
      analytics: '/api/analytics',
      test: '/api/test'
    }
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Shopify API Server Running' });
});

// Get shop information
app.get('/api/shop', async (req, res) => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    res.json(mockShopInfo);
  } catch (error) {
    console.error('Error fetching shop info:', error);
    res.status(500).json({ error: 'Failed to fetch shop information' });
  }
});

// Get products
app.get('/api/products', async (req, res) => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));
    res.json(mockProducts);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
});

// Get selling plans
app.get('/api/selling-plans', async (req, res) => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 600));
    res.json([]);
  } catch (error) {
    console.error('Error fetching selling plans:', error);
    res.status(500).json({ error: 'Failed to fetch selling plans' });
  }
});

// Get analytics data
app.get('/api/analytics', async (req, res) => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    res.json(mockAnalytics);
  } catch (error) {
    console.error('Error fetching analytics:', error);
    res.status(500).json({ error: 'Failed to fetch analytics' });
  }
});

// Update product status
app.put('/api/products/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // Find and update the product in mock data
    const product = mockProducts.find(p => p.id === id);
    if (product) {
      product.status = status.toUpperCase();
      res.json(product);
    } else {
      res.status(404).json({ error: 'Product not found' });
    }
  } catch (error) {
    console.error('Error updating product status:', error);
    res.status(500).json({ error: 'Failed to update product status' });
  }
});

// Test Shopify connection
app.get('/api/test', async (req, res) => {
  try {
    res.json({
      success: true,
      message: 'Backend API is working',
      shopInfo: mockShopInfo
    });
  } catch (error) {
    console.error('Error testing connection:', error);
    res.status(500).json({ error: 'Connection test failed' });
  }
});

app.listen(port, () => {
  console.log(`🚀 Shopify API Server running on http://localhost:${port}`);
  console.log(`📊 Dashboard available at http://localhost:3000`);
  console.log(`🛍️  Connected to store: 9bc094.myshopify.com`);
});
