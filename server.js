const express = require('express');
const cors = require('cors');
const ShopifyAPI = require('./shopify-api');

const app = express();
const port = 3001;

// Enable CORS for frontend
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));

app.use(express.json());

// Initialize Shopify API
const shopify = new ShopifyAPI();

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Big River Coffee - Shopify API Server',
    status: 'Running',
    endpoints: {
      health: '/health',
      shop: '/api/shop',
      products: '/api/products',
      analytics: '/api/analytics',
      test: '/api/test'
    }
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Shopify API Server Running' });
});

// Get shop information
app.get('/api/shop', async (req, res) => {
  try {
    const shopInfo = await shopify.getShopInfo();
    res.json(shopInfo);
  } catch (error) {
    console.error('Error fetching shop info:', error);
    res.status(500).json({ error: 'Failed to fetch shop information' });
  }
});

// Get products
app.get('/api/products', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const products = await shopify.getProducts(limit);
    res.json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
});

// Get selling plans
app.get('/api/selling-plans', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    const sellingPlans = await shopify.getSellingPlans(limit);
    res.json(sellingPlans);
  } catch (error) {
    console.error('Error fetching selling plans:', error);
    res.status(500).json({ error: 'Failed to fetch selling plans' });
  }
});

// Get analytics data
app.get('/api/analytics', async (req, res) => {
  try {
    // Get products and orders for analytics
    const [products, orders] = await Promise.all([
      shopify.getProducts(250),
      shopify.getOrders(250)
    ]);

    // Calculate analytics
    const totalProducts = products.length;
    const activeProducts = products.filter(p => p.status === 'ACTIVE').length;
    const draftProducts = products.filter(p => p.status === 'DRAFT').length;

    // Calculate revenue from orders
    const totalRevenue = orders.reduce((sum, order) => {
      return sum + parseFloat(order.total_price || 0);
    }, 0);

    // Top selling products (simplified)
    const topSellingProducts = products
      .map(product => ({
        name: product.title,
        sales: Math.floor(Math.random() * 200), // Would need order line items for real data
        revenue: Math.floor(Math.random() * 5000)
      }))
      .sort((a, b) => b.sales - a.sales)
      .slice(0, 5);

    // Mock sales data for charts (would need historical order data)
    const salesData = [
      { month: 'Jan', sales: 1200, revenue: 24000 },
      { month: 'Feb', sales: 1350, revenue: 27000 },
      { month: 'Mar', sales: 1100, revenue: 22000 },
      { month: 'Apr', sales: 1450, revenue: 29000 },
      { month: 'May', sales: 1600, revenue: 32000 },
      { month: 'Jun', sales: 1750, revenue: 35000 }
    ];

    const analytics = {
      totalProducts,
      activeProducts,
      draftProducts,
      totalRevenue,
      monthlyGrowth: 12.5,
      topSellingProducts,
      salesData,
      recentOrders: orders.slice(0, 10)
    };

    res.json(analytics);
  } catch (error) {
    console.error('Error fetching analytics:', error);
    res.status(500).json({ error: 'Failed to fetch analytics' });
  }
});

// Update product status
app.put('/api/products/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    const updatedProduct = await shopify.updateProductStatus(id, status);
    res.json(updatedProduct);
  } catch (error) {
    console.error('Error updating product status:', error);
    res.status(500).json({ error: 'Failed to update product status' });
  }
});

// Test Shopify connection
app.get('/api/test', async (req, res) => {
  try {
    const result = await shopify.testConnection();
    res.json(result);
  } catch (error) {
    console.error('Error testing connection:', error);
    res.status(500).json({ error: 'Connection test failed' });
  }
});

app.listen(port, () => {
  console.log(`🚀 Shopify API Server running on http://localhost:${port}`);
  console.log(`📊 Dashboard available at http://localhost:3000`);
  console.log(`🛍️  Connected to store: 9bc094.myshopify.com`);
});
