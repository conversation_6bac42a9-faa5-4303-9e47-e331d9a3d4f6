import axios from 'axios'

// Shopify API Configuration
const SHOPIFY_CONFIG = {
  shopDomain: '9bc094',
  accessToken: '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',
  apiVersion: '2025-07'
}

const API_BASE_URL = `https://${SHOPIFY_CONFIG.shopDomain}.myshopify.com/admin/api/${SHOPIFY_CONFIG.apiVersion}`

// Create axios instance with default headers
const shopifyApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'X-Shopify-Access-Token': SHOPIFY_CONFIG.accessToken,
    'Content-Type': 'application/json'
  }
})

// GraphQL endpoint
const GRAPHQL_ENDPOINT = `${API_BASE_URL}/graphql.json`

// GraphQL query helper
const graphqlQuery = async (query, variables = {}) => {
  try {
    const response = await axios.post(GRAPHQL_ENDPOINT, {
      query,
      variables
    }, {
      headers: {
        'X-Shopify-Access-Token': SHOPIFY_CONFIG.accessToken,
        'Content-Type': 'application/json'
      }
    })

    if (response.data.errors) {
      console.error('GraphQL Errors:', response.data.errors)
      throw new Error('GraphQL query failed')
    }

    return response.data
  } catch (error) {
    console.error('API Request Error:', error.response?.data || error.message)
    throw error
  }
}

// API Functions
export const shopifyApiService = {
  // Get shop information
  async getShopInfo() {
    const query = `
      query {
        shop {
          name
          email
          myshopifyDomain
          currencyCode
          plan {
            displayName
          }
        }
      }
    `
    const result = await graphqlQuery(query)
    return result.data.shop
  },

  // Get all products with detailed information
  async getProducts(first = 50) {
    const query = `
      query getProducts($first: Int!) {
        products(first: $first) {
          edges {
            node {
              id
              title
              handle
              description
              productType
              vendor
              status
              createdAt
              updatedAt
              tags
              totalInventory
              images(first: 5) {
                edges {
                  node {
                    id
                    url
                    altText
                  }
                }
              }
              variants(first: 10) {
                edges {
                  node {
                    id
                    title
                    price
                    compareAtPrice
                    sku
                    inventoryQuantity
                    availableForSale
                  }
                }
              }
              sellingPlanGroups(first: 10) {
                edges {
                  node {
                    id
                    name
                    merchantCode
                  }
                }
              }
            }
          }
          pageInfo {
            hasNextPage
            hasPreviousPage
          }
        }
      }
    `
    const result = await graphqlQuery(query, { first })
    return result.data.products.edges.map(edge => ({
      ...edge.node,
      // Transform for easier use in components
      images: edge.node.images.edges.map(img => ({
        src: img.node.url,
        alt: img.node.altText || edge.node.title
      })),
      variants: edge.node.variants.edges.map(variant => variant.node),
      sellingPlans: edge.node.sellingPlanGroups.edges.map(group => group.node)
    }))
  },

  // Get selling plan groups
  async getSellingPlans(first = 10) {
    const query = `
      query getSellingPlans($first: Int!) {
        sellingPlanGroups(first: $first) {
          edges {
            node {
              id
              name
              merchantCode
              description
              summary
              createdAt
              sellingPlans(first: 10) {
                edges {
                  node {
                    id
                    name
                    description
                    billingPolicy {
                      ... on SellingPlanRecurringBillingPolicy {
                        interval
                        intervalCount
                      }
                    }
                    deliveryPolicy {
                      ... on SellingPlanRecurringDeliveryPolicy {
                        interval
                        intervalCount
                      }
                    }
                  }
                }
              }
              productVariants(first: 20) {
                edges {
                  node {
                    id
                    title
                    product {
                      id
                      title
                    }
                  }
                }
              }
            }
          }
        }
      }
    `
    const result = await graphqlQuery(query, { first })
    return result.data.sellingPlanGroups.edges.map(edge => ({
      ...edge.node,
      sellingPlans: edge.node.sellingPlans.edges.map(plan => plan.node),
      productVariants: edge.node.productVariants.edges.map(variant => variant.node)
    }))
  },

  // Update product status using REST API
  async updateProductStatus(productId, status) {
    try {
      const response = await shopifyApi.put(`/products/${productId}.json`, {
        product: {
          id: productId,
          status: status
        }
      })
      return response.data.product
    } catch (error) {
      console.error('Error updating product status:', error)
      throw error
    }
  },

  // Get orders for analytics (last 30 days)
  async getRecentOrders(limit = 50) {
    try {
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      
      const response = await shopifyApi.get('/orders.json', {
        params: {
          limit,
          status: 'any',
          created_at_min: thirtyDaysAgo.toISOString()
        }
      })
      return response.data.orders
    } catch (error) {
      console.error('Error fetching orders:', error)
      throw error
    }
  },

  // Get analytics data
  async getAnalytics() {
    try {
      // Get products for product count
      const products = await this.getProducts(250)
      
      // Get recent orders for revenue calculation
      const orders = await this.getRecentOrders(250)
      
      // Calculate metrics
      const totalProducts = products.length
      const activeProducts = products.filter(p => p.status === 'ACTIVE').length
      const draftProducts = products.filter(p => p.status === 'DRAFT').length
      
      // Calculate revenue from orders
      const totalRevenue = orders.reduce((sum, order) => {
        return sum + parseFloat(order.total_price || 0)
      }, 0)
      
      // Calculate monthly growth (simplified)
      const monthlyGrowth = 12.5 // Placeholder - would need historical data
      
      // Top selling products (simplified - based on inventory sold)
      const topSellingProducts = products
        .map(product => ({
          name: product.title,
          sales: Math.floor(Math.random() * 200), // Placeholder
          revenue: Math.floor(Math.random() * 5000) // Placeholder
        }))
        .sort((a, b) => b.sales - a.sales)
        .slice(0, 5)
      
      // Sales data for charts (placeholder - would need historical data)
      const salesData = [
        { month: 'Jan', sales: 1200, revenue: 24000 },
        { month: 'Feb', sales: 1350, revenue: 27000 },
        { month: 'Mar', sales: 1100, revenue: 22000 },
        { month: 'Apr', sales: 1450, revenue: 29000 },
        { month: 'May', sales: 1600, revenue: 32000 },
        { month: 'Jun', sales: 1750, revenue: 35000 }
      ]
      
      return {
        totalProducts,
        activeProducts,
        draftProducts,
        totalRevenue,
        monthlyGrowth,
        topSellingProducts,
        salesData,
        recentOrders: orders.slice(0, 10) // Last 10 orders
      }
    } catch (error) {
      console.error('Error getting analytics:', error)
      throw error
    }
  },

  // Test API connection
  async testConnection() {
    try {
      const shopInfo = await this.getShopInfo()
      return { success: true, shopInfo }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }
}

export default shopifyApiService
